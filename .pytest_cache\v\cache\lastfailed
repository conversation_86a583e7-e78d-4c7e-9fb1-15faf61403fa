{"tests/test_skill_learning_system.py::TestSkillLearningSystem::test_action_recording": true, "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_action_analysis_for_learning": true, "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_skill_learning_hints": true, "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_multiple_skill_learning_triggers": true, "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_tutorial_boost": true, "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_progressive_scaling": true, "tests/test_response_consistency.py::TestResponseConsistency::test_analyze_character_state": true, "tests/test_response_consistency.py::TestResponseConsistency::test_build_narrative_continuity": true, "tests/test_response_consistency.py::TestResponseConsistency::test_post_process_response_basic_cleanup": true, "tests/test_response_consistency.py::TestResponseConsistency::test_post_process_response_capitalization": true, "tests/test_circumstantial_skills.py::TestCircumstantialSkills::test_creature_specific_environmental_interactions": true}